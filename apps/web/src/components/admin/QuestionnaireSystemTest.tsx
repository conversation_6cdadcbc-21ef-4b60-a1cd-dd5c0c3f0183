import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  CheckCircle,
  Error,
  Warning,
  Info,
  PlayArrow,
  Stop
} from '@mui/icons-material';
import axiosInstance from '../../services/axios';
import routes from '../../services/apiRoutes';
import { QuestionnaireConfig, SectionConfig } from '../../types/questionnaire-admin';

interface TestResult {
  test: string;
  status: 'pass' | 'fail' | 'warning' | 'info';
  message: string;
  details?: string;
}

interface SystemTestResults {
  database: TestResult[];
  api: TestResult[];
  frontend: TestResult[];
  sections: TestResult[];
  overall: 'pass' | 'fail' | 'warning';
}

const QuestionnaireSystemTest: React.FC = () => {
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState<SystemTestResults | null>(null);
  const [progress, setProgress] = useState(0);

  const runTests = async () => {
    setTesting(true);
    setProgress(0);
    
    const testResults: SystemTestResults = {
      database: [],
      api: [],
      frontend: [],
      sections: [],
      overall: 'pass'
    };

    try {
      // Test 1: API Connectivity
      setProgress(10);
      try {
        const response = await axiosInstance.get(routes.GET_QUESTIONNAIRE_CONFIGS);
        testResults.api.push({
          test: 'API Connectivity',
          status: 'pass',
          message: 'Successfully connected to questionnaire API',
          details: `Retrieved ${response.data.data?.length || 0} questionnaires`
        });
      } catch (error) {
        testResults.api.push({
          test: 'API Connectivity',
          status: 'fail',
          message: 'Failed to connect to questionnaire API',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
        testResults.overall = 'fail';
      }

      // Test 2: Questionnaire Data Structure
      setProgress(25);
      try {
        const response = await axiosInstance.get(routes.GET_QUESTIONNAIRE_CONFIGS);
        const questionnaires = response.data.data || [];
        
        if (questionnaires.length === 0) {
          testResults.database.push({
            test: 'Questionnaire Data',
            status: 'warning',
            message: 'No questionnaires found in database',
            details: 'Run the migration to populate questionnaire data'
          });
          testResults.overall = 'warning';
        } else {
          testResults.database.push({
            test: 'Questionnaire Data',
            status: 'pass',
            message: `Found ${questionnaires.length} questionnaires`,
            details: questionnaires.map((q: any) => q.name).join(', ')
          });
        }
      } catch (error) {
        testResults.database.push({
          test: 'Questionnaire Data',
          status: 'fail',
          message: 'Failed to retrieve questionnaire data',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
        testResults.overall = 'fail';
      }

      // Test 3: Section Support
      setProgress(50);
      try {
        const response = await axiosInstance.get(`${routes.GET_QUESTIONNAIRE_CONFIGS}/THC_INCREASE`);
        const questionnaire: QuestionnaireConfig = response.data.data;
        
        if (questionnaire.sections && questionnaire.sections.length > 0) {
          testResults.sections.push({
            test: 'Section Structure',
            status: 'pass',
            message: `THC_INCREASE has ${questionnaire.sections.length} sections`,
            details: questionnaire.sections.map(s => s.title).join(', ')
          });
        } else {
          testResults.sections.push({
            test: 'Section Structure',
            status: 'warning',
            message: 'No sections found for THC_INCREASE questionnaire',
            details: 'Run the sections migration to add section support'
          });
          testResults.overall = 'warning';
        }
      } catch (error) {
        testResults.sections.push({
          test: 'Section Structure',
          status: 'fail',
          message: 'Failed to test section structure',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
        testResults.overall = 'fail';
      }

      // Test 4: Question Types Support
      setProgress(75);
      try {
        const response = await axiosInstance.get(`${routes.GET_QUESTIONNAIRE_CONFIGS}/THC_INCREASE`);
        const questionnaire: QuestionnaireConfig = response.data.data;
        
        if (questionnaire.questions && questionnaire.questions.length > 0) {
          const questionTypes = [...new Set(questionnaire.questions.map(q => q.type))];
          testResults.frontend.push({
            test: 'Question Types',
            status: 'pass',
            message: `Supports ${questionTypes.length} question types`,
            details: questionTypes.join(', ')
          });
        } else {
          testResults.frontend.push({
            test: 'Question Types',
            status: 'warning',
            message: 'No questions found to test question types',
            details: 'Populate questionnaire with questions'
          });
          testResults.overall = 'warning';
        }
      } catch (error) {
        testResults.frontend.push({
          test: 'Question Types',
          status: 'fail',
          message: 'Failed to test question types',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
        testResults.overall = 'fail';
      }

      // Test 5: Scoring Calculation
      setProgress(90);
      try {
        const response = await axiosInstance.get(`${routes.GET_QUESTIONNAIRE_CONFIGS}/THC_INCREASE`);
        const questionnaire: QuestionnaireConfig = response.data.data;
        
        if (questionnaire.maxScore && questionnaire.maxScore > 0) {
          testResults.frontend.push({
            test: 'Scoring System',
            status: 'pass',
            message: `Max score calculated: ${questionnaire.maxScore}`,
            details: `Threshold: ${questionnaire.currentThreshold || 0}/${questionnaire.maxScore}`
          });
        } else {
          testResults.frontend.push({
            test: 'Scoring System',
            status: 'warning',
            message: 'No scoring data available',
            details: 'Add questions with scores to test scoring system'
          });
          testResults.overall = 'warning';
        }
      } catch (error) {
        testResults.frontend.push({
          test: 'Scoring System',
          status: 'fail',
          message: 'Failed to test scoring system',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
        testResults.overall = 'fail';
      }

      setProgress(100);
      setResults(testResults);
    } catch (error) {
      testResults.overall = 'fail';
      testResults.api.push({
        test: 'System Test',
        status: 'fail',
        message: 'System test failed with unexpected error',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
      setResults(testResults);
    } finally {
      setTesting(false);
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pass': return <CheckCircle color="success" />;
      case 'fail': return <Error color="error" />;
      case 'warning': return <Warning color="warning" />;
      case 'info': return <Info color="info" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pass': return 'success';
      case 'fail': return 'error';
      case 'warning': return 'warning';
      case 'info': return 'info';
    }
  };

  return (
    <Box>
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h5" gutterBottom>
            Section-Based Questionnaire System Test
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            This test validates the complete section-based questionnaire system including database structure,
            API endpoints, frontend components, and section management functionality.
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <Button
              variant="contained"
              startIcon={testing ? <Stop /> : <PlayArrow />}
              onClick={runTests}
              disabled={testing}
            >
              {testing ? 'Testing...' : 'Run System Test'}
            </Button>
            
            {results && (
              <Chip
                label={`Overall: ${results.overall.toUpperCase()}`}
                color={getStatusColor(results.overall) as any}
                variant="outlined"
              />
            )}
          </Box>

          {testing && (
            <Box sx={{ mt: 2 }}>
              <LinearProgress variant="determinate" value={progress} />
              <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
                Running tests... {progress}%
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {results && (
        <Box>
          {/* Database Tests */}
          <Accordion defaultExpanded>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">Database Tests</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List>
                {results.database.map((result, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>{getStatusIcon(result.status)}</ListItemIcon>
                    <ListItemText
                      primary={result.test}
                      secondary={
                        <Box>
                          <Typography variant="body2">{result.message}</Typography>
                          {result.details && (
                            <Typography variant="caption" color="text.secondary">
                              {result.details}
                            </Typography>
                          )}
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>

          {/* API Tests */}
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">API Tests</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List>
                {results.api.map((result, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>{getStatusIcon(result.status)}</ListItemIcon>
                    <ListItemText
                      primary={result.test}
                      secondary={
                        <Box>
                          <Typography variant="body2">{result.message}</Typography>
                          {result.details && (
                            <Typography variant="caption" color="text.secondary">
                              {result.details}
                            </Typography>
                          )}
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>

          {/* Section Tests */}
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">Section Tests</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List>
                {results.sections.map((result, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>{getStatusIcon(result.status)}</ListItemIcon>
                    <ListItemText
                      primary={result.test}
                      secondary={
                        <Box>
                          <Typography variant="body2">{result.message}</Typography>
                          {result.details && (
                            <Typography variant="caption" color="text.secondary">
                              {result.details}
                            </Typography>
                          )}
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>

          {/* Frontend Tests */}
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">Frontend Tests</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List>
                {results.frontend.map((result, index) => (
                  <ListItem key={index}>
                    <ListItemIcon>{getStatusIcon(result.status)}</ListItemIcon>
                    <ListItemText
                      primary={result.test}
                      secondary={
                        <Box>
                          <Typography variant="body2">{result.message}</Typography>
                          {result.details && (
                            <Typography variant="caption" color="text.secondary">
                              {result.details}
                            </Typography>
                          )}
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </AccordionDetails>
          </Accordion>
        </Box>
      )}
    </Box>
  );
};

export default QuestionnaireSystemTest;
