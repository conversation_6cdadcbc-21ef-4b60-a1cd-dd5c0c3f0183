import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Code as CodeIcon,
  PlayArrow as TestIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Edit as EditIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { QuestionConfig, SpecialLogicConfig } from '../../types/questionnaire-admin';

interface SpecialLogicQuestionEditorProps {
  question: QuestionConfig;
  onUpdate: (question: QuestionConfig) => void;
  readOnly?: boolean;
}

interface TestResult {
  input: string;
  expectedScore: number;
  actualScore: number;
  passed: boolean;
}

const SpecialLogicQuestionEditor: React.FC<SpecialLogicQuestionEditorProps> = ({
  question,
  onUpdate,
  readOnly = false
}) => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [testInput, setTestInput] = useState('');
  const [showTestDialog, setShowTestDialog] = useState(false);
  const [isRunningTests, setIsRunningTests] = useState(false);

  const specialLogic = question.specialLogic;

  if (!specialLogic) {
    return (
      <Alert severity="error">
        Special logic configuration is missing for this question.
      </Alert>
    );
  }

  const runSingleTest = (input: string): number => {
    try {
      // This is a simplified test runner - in production, this would be more secure
      // and would run in a sandboxed environment
      const func = new Function('value', specialLogic.codeSnippet);
      return func(input);
    } catch (error) {
      console.error('Error running special logic:', error);
      return 0;
    }
  };

  const runAllTests = async () => {
    setIsRunningTests(true);
    const results: TestResult[] = [];

    for (const testCase of specialLogic.testCases || []) {
      const actualScore = runSingleTest(testCase.input);
      results.push({
        input: testCase.input,
        expectedScore: testCase.expectedScore,
        actualScore,
        passed: actualScore === testCase.expectedScore
      });
    }

    setTestResults(results);
    setIsRunningTests(false);
  };

  const runCustomTest = () => {
    if (!testInput.trim()) return;
    
    const actualScore = runSingleTest(testInput);
    const customResult: TestResult = {
      input: testInput,
      expectedScore: 0, // Unknown for custom tests
      actualScore,
      passed: true // Always true for custom tests since we don't know expected
    };

    setTestResults([customResult, ...testResults]);
    setTestInput('');
    setShowTestDialog(false);
  };

  const allTestsPassed = testResults.length > 0 && testResults.every(r => r.passed);

  return (
    <Box>
      {/* Warning Banner */}
      <Alert 
        severity="warning" 
        icon={<WarningIcon />}
        sx={{ mb: 2 }}
      >
        <Typography variant="subtitle2" gutterBottom>
          Developer Required
        </Typography>
        This question uses special logic that requires developer intervention to modify.
        Changes to the scoring logic must be reviewed and tested by a developer.
      </Alert>

      {/* Question Overview */}
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            {question.text}
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            {specialLogic.description}
          </Typography>
          
          <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
            <Chip 
              label={`${specialLogic.testCases?.length || 0} Test Cases`}
              color="primary"
              size="small"
            />
            <Chip 
              label={specialLogic.requiresDeveloper ? 'Developer Required' : 'Self-Service'}
              color={specialLogic.requiresDeveloper ? 'warning' : 'success'}
              size="small"
            />
            <Chip 
              label={`Last Modified: ${new Date(specialLogic.lastModified).toLocaleDateString()}`}
              variant="outlined"
              size="small"
            />
          </Box>
        </CardContent>
      </Card>

      {/* Code Display */}
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <CodeIcon />
            <Typography variant="h6">Scoring Logic</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ position: 'relative' }}>
            <Paper 
              sx={{ 
                p: 2, 
                backgroundColor: '#f5f5f5', 
                fontFamily: 'monospace',
                fontSize: '0.875rem',
                overflow: 'auto'
              }}
            >
              <pre>{specialLogic.codeSnippet}</pre>
            </Paper>
            {!readOnly && (
              <Tooltip title="Edit code (Developer only)">
                <IconButton
                  sx={{ position: 'absolute', top: 8, right: 8 }}
                  size="small"
                  disabled
                >
                  <EditIcon />
                </IconButton>
              </Tooltip>
            )}
          </Box>
        </AccordionDetails>
      </Accordion>

      {/* Test Cases */}
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <TestIcon />
            <Typography variant="h6">Test Cases</Typography>
            {testResults.length > 0 && (
              <Chip 
                label={allTestsPassed ? 'All Passed' : 'Some Failed'}
                color={allTestsPassed ? 'success' : 'error'}
                size="small"
              />
            )}
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ mb: 2 }}>
            <Button
              variant="contained"
              startIcon={<TestIcon />}
              onClick={runAllTests}
              disabled={isRunningTests}
              sx={{ mr: 1 }}
            >
              {isRunningTests ? 'Running Tests...' : 'Run All Tests'}
            </Button>
            <Button
              variant="outlined"
              startIcon={<AddIcon />}
              onClick={() => setShowTestDialog(true)}
            >
              Custom Test
            </Button>
          </Box>

          {/* Test Cases Table */}
          <TableContainer component={Paper}>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Input</TableCell>
                  <TableCell>Expected Score</TableCell>
                  <TableCell>Actual Score</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Description</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {specialLogic.testCases?.map((testCase, index) => {
                  const result = testResults.find(r => r.input === testCase.input);
                  return (
                    <TableRow key={index}>
                      <TableCell>
                        <code>{testCase.input}</code>
                      </TableCell>
                      <TableCell>{testCase.expectedScore}</TableCell>
                      <TableCell>
                        {result ? result.actualScore : '-'}
                      </TableCell>
                      <TableCell>
                        {result && (
                          <Chip
                            label={result.passed ? 'PASS' : 'FAIL'}
                            color={result.passed ? 'success' : 'error'}
                            size="small"
                          />
                        )}
                      </TableCell>
                      <TableCell>{testCase.description}</TableCell>
                    </TableRow>
                  );
                })}
                
                {/* Custom test results */}
                {testResults
                  .filter(r => !specialLogic.testCases?.some(tc => tc.input === r.input))
                  .map((result, index) => (
                    <TableRow key={`custom-${index}`}>
                      <TableCell>
                        <code>{result.input}</code>
                      </TableCell>
                      <TableCell>-</TableCell>
                      <TableCell>{result.actualScore}</TableCell>
                      <TableCell>
                        <Chip
                          label="CUSTOM"
                          color="info"
                          size="small"
                        />
                      </TableCell>
                      <TableCell>Custom test</TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </TableContainer>
        </AccordionDetails>
      </Accordion>

      {/* Answer Options Display */}
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <InfoIcon />
            <Typography variant="h6">Answer Options</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <Typography variant="body2" color="text.secondary" paragraph>
            These are the available answer options. The actual scoring is determined by the special logic above.
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {question.answerOptions?.map((option, index) => (
              <Chip
                key={index}
                label={`${option.label} (${option.score} pts)`}
                variant="outlined"
              />
            ))}
          </Box>
        </AccordionDetails>
      </Accordion>

      {/* Custom Test Dialog */}
      <Dialog open={showTestDialog} onClose={() => setShowTestDialog(false)}>
        <DialogTitle>Run Custom Test</DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Test Input"
            value={testInput}
            onChange={(e) => setTestInput(e.target.value)}
            placeholder="Enter a test value..."
            sx={{ mt: 1 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowTestDialog(false)}>Cancel</Button>
          <Button onClick={runCustomTest} variant="contained">
            Run Test
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SpecialLogicQuestionEditor;
