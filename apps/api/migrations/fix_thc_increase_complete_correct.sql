-- COMPLETE THC INCREASE QUESTIONNAIRE FIX - EXACT SPECIFICATION MAPPING
-- Based on docs/thc_increase_questions_and_answers.json
-- This adds ALL 16 questions with correct scoring (max 61, threshold 45)

-- Update main config (already correct but ensuring consistency)
UPDATE questionnaire_configs 
SET 
  name = 'THC Increase (29% THC)',
  max_score = 61,
  threshold = 45,
  sections_count = 8,
  last_modified = NOW(),
  modified_by = 'system_migration_complete_correct'
WHERE id = 'thc_increase';

-- Clear existing sections and recreate with proper structure
DELETE FROM questionnaire_sections WHERE questionnaire_id = 'thc_increase';

INSERT INTO questionnaire_sections (id, questionnaire_id, title, description, order_index, validation_rules, is_active) VALUES
('f1a2b3c4-d5e6-4890-abcd-ef1234567890', 'thc_increase', 'Trial Usage Assessment', 'How consistent were you in using 22% THC flower during the two-week trial?', 1, '{"requireAllQuestions": true}', true),
('g2b3c4d5-e6f7-4901-bcde-f23456789012', 'thc_increase', 'Condition and Treatment Effectiveness', 'What condition are you treating and how effective has it been?', 2, '{"requireAllQuestions": true}', true),
('h3c4d5e6-f7a8-4012-cdef-345678901234', 'thc_increase', 'Symptoms and Side Effects Assessment', 'Changes in symptoms and side effects experienced', 3, '{"requireAllQuestions": true}', true),
('i4d5e6f7-a8b9-4123-def0-456789012345', 'thc_increase', 'Side Effect Management and Concerns', 'How manageable are side effects and any concerns', 4, '{"requireAllQuestions": true}', true),
('j5e6f7a8-b9c0-4234-ef01-567890123456', 'thc_increase', 'Treatment Effectiveness Assessment', 'Overall effectiveness and strength assessment', 5, '{"requireAllQuestions": true}', true),
('k6f7a8b9-c0d1-4345-f012-678901234567', 'thc_increase', 'Relief and Treatment Satisfaction', 'Relief during episodes and satisfaction with form', 6, '{"requireAllQuestions": true}', true),
('l7a8b9c0-d1e2-4456-0123-789012345678', 'thc_increase', 'Future Treatment Preferences', 'Openness to higher potency and importance of quick relief', 7, '{"requireAllQuestions": true}', true),
('m8b9c0d1-e2f3-4567-1234-890123456789', 'thc_increase', 'Overall Treatment Experience', 'Likelihood to continue and overall satisfaction', 8, '{"requireAllQuestions": true}', true);

-- Replace ALL questions with COMPLETE specification mapping (16 questions total)
UPDATE questionnaire_configs 
SET questions = '[
  {
    "key": "consistency",
    "text": "How consistent were you in using 22% THC flower during the two-week trial?",
    "type": "radio",
    "order": 1,
    "sectionId": "f1a2b3c4-d5e6-4890-abcd-ef1234567890",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "every-day",
        "label": "Every day as prescribed",
        "score": 3
      },
      {
        "value": "most-days",
        "label": "Most days",
        "score": 2
      },
      {
        "value": "occasionally",
        "label": "Occasionally",
        "score": 1
      },
      {
        "value": "rarely",
        "label": "Rarely",
        "score": 0
      }
    ]
  },
  {
    "key": "dosage",
    "text": "What dosage of 22% THC flower were you taking during the trial?",
    "type": "radio",
    "order": 2,
    "sectionId": "f1a2b3c4-d5e6-4890-abcd-ef1234567890",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "less-than-0-5g",
        "label": "Less than 0.5g/day",
        "score": 1
      },
      {
        "value": "0-5g-1g",
        "label": "0.5g - 1g/day",
        "score": 2
      },
      {
        "value": "1g-2g",
        "label": "1g - 2g/day",
        "score": 3
      },
      {
        "value": "more-than-2g",
        "label": "More than 2g/day",
        "score": 4
      }
    ]
  },
  {
    "key": "frequency",
    "text": "How often did you use 22% THC flower?",
    "type": "radio",
    "order": 3,
    "sectionId": "f1a2b3c4-d5e6-4890-abcd-ef1234567890",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "once-a-day",
        "label": "Once a day",
        "score": 1
      },
      {
        "value": "twice-a-day",
        "label": "Twice a day",
        "score": 2
      },
      {
        "value": "three-times-a-day",
        "label": "Three times a day",
        "score": 3
      },
      {
        "value": "as-needed",
        "label": "As needed",
        "score": 1
      }
    ]
  },
  {
    "key": "condition",
    "text": "What condition are you using THC flower to treat?",
    "type": "radio",
    "order": 4,
    "sectionId": "g2b3c4d5-e6f7-4901-bcde-f23456789012",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "chronic-pain",
        "label": "Chronic pain",
        "score": 0
      },
      {
        "value": "anxiety",
        "label": "Anxiety",
        "score": 0
      },
      {
        "value": "insomnia",
        "label": "Insomnia",
        "score": 0
      },
      {
        "value": "inflammation",
        "label": "Inflammation",
        "score": 0
      },
      {
        "value": "other",
        "label": "Other (Please specify):",
        "score": 0
      }
    ]
  },
  {
    "key": "conditionOther",
    "text": "Please specify your condition...",
    "type": "text",
    "order": 5,
    "sectionId": "g2b3c4d5-e6f7-4901-bcde-f23456789012",
    "textFieldConfig": {
      "required": false,
      "maxLength": 200,
      "placeholder": "Type your condition here"
    },
    "dependsOnQuestion": "condition",
    "contributesToScore": false
  },
  {
    "key": "effectiveness",
    "text": "On a scale of 1 to 10, how effective has 22% THC flower been in managing your symptoms?",
    "type": "radio",
    "order": 6,
    "sectionId": "g2b3c4d5-e6f7-4901-bcde-f23456789012",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "1-2",
        "label": "1-2",
        "score": 0
      },
      {
        "value": "3-4",
        "label": "3-4",
        "score": 1
      },
      {
        "value": "5-6",
        "label": "5-6",
        "score": 2
      },
      {
        "value": "7-8",
        "label": "7-8",
        "score": 3
      },
      {
        "value": "9-10",
        "label": "9-10",
        "score": 4
      }
    ]
  }
]'::jsonb
WHERE id = 'thc_increase';

-- Add remaining 10 questions (Steps 3-8) to complete the 16 questions
UPDATE questionnaire_configs
SET questions = questions || '[
  {
    "key": "symptomChanges",
    "text": "Have you noticed any changes in your symptoms since starting 22% THC flower?",
    "type": "radio",
    "order": 7,
    "sectionId": "h3c4d5e6-f7a8-4012-cdef-345678901234",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "significant-improvement",
        "label": "Significant improvement",
        "score": 4
      },
      {
        "value": "some-improvement",
        "label": "Some improvement",
        "score": 3
      },
      {
        "value": "no-change",
        "label": "No change",
        "score": 1
      },
      {
        "value": "worsening-symptoms",
        "label": "Worsening of symptoms",
        "score": 0
      }
    ]
  },
  {
    "key": "sideEffect",
    "text": "Have you experienced any side effects from using 22% THC flower during the trial?",
    "type": "radio",
    "order": 8,
    "sectionId": "h3c4d5e6-f7a8-4012-cdef-345678901234",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "drowsiness",
        "label": "Drowsiness",
        "score": 1
      },
      {
        "value": "dry-mouth",
        "label": "Dry mouth",
        "score": 1
      },
      {
        "value": "dizziness",
        "label": "Dizziness",
        "score": 1
      },
      {
        "value": "increased-heart-rate",
        "label": "Increased heart rate",
        "score": 1
      },
      {
        "value": "anxiety",
        "label": "Anxiety",
        "score": 1
      },
      {
        "value": "none",
        "label": "None",
        "score": 4
      },
      {
        "value": "other",
        "label": "Other (Please specify):",
        "score": 0
      }
    ]
  },
  {
    "key": "sideEffectsOther",
    "text": "Please specify other side effects...",
    "type": "text",
    "order": 9,
    "sectionId": "h3c4d5e6-f7a8-4012-cdef-345678901234",
    "textFieldConfig": {
      "required": false,
      "maxLength": 200,
      "placeholder": "Other (Please specify)"
    },
    "dependsOnQuestion": "sideEffect",
    "contributesToScore": false
  },
  {
    "key": "sideEffectManageability",
    "text": "On a scale of 1 to 10, how manageable were these side effects?",
    "type": "radio",
    "order": 10,
    "sectionId": "i4d5e6f7-a8b9-4123-def0-456789012345",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "1-2",
        "label": "1-2",
        "score": 0
      },
      {
        "value": "3-4",
        "label": "3-4",
        "score": 1
      },
      {
        "value": "5-6",
        "label": "5-6",
        "score": 2
      },
      {
        "value": "7-8",
        "label": "7-8",
        "score": 3
      },
      {
        "value": "9-10",
        "label": "9-10",
        "score": 4
      }
    ]
  },
  {
    "key": "concerns",
    "text": "Did you have any concerns or issues with your THC flower treatment during the trial?",
    "type": "radio",
    "order": 11,
    "sectionId": "i4d5e6f7-a8b9-4123-def0-456789012345",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "yes",
        "label": "Yes",
        "score": 0
      },
      {
        "value": "no",
        "label": "No",
        "score": 3
      }
    ]
  },
  {
    "key": "treatmentEffectiveness",
    "text": "Was the 22% THC flower effective in treating your condition?",
    "type": "radio",
    "order": 12,
    "sectionId": "j5e6f7a8-b9c0-4234-ef01-567890123456",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "very-effective",
        "label": "Very effective",
        "score": 4
      },
      {
        "value": "effective",
        "label": "Effective",
        "score": 3
      },
      {
        "value": "somewhat-effective",
        "label": "Somewhat effective",
        "score": 2
      },
      {
        "value": "not-effective",
        "label": "Not effective",
        "score": 0
      }
    ]
  }
]'::jsonb
WHERE id = 'thc_increase';

-- Add final 4 questions to complete all 16 questions (Steps 5-8 remaining parts)
UPDATE questionnaire_configs
SET questions = questions || '[
  {
    "key": "weaknessAssessment",
    "text": "Do you feel the 22% THC flower was too weak in managing your symptoms?",
    "type": "radio",
    "order": 13,
    "sectionId": "j5e6f7a8-b9c0-4234-ef01-567890123456",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "yes-definitely",
        "label": "Yes, definitely",
        "score": 4
      },
      {
        "value": "yes-somewhat",
        "label": "Yes, somewhat",
        "score": 3
      },
      {
        "value": "no-adequate",
        "label": "No, it was adequate",
        "score": 1
      },
      {
        "value": "no-too-strong",
        "label": "No, it was too strong",
        "score": 0
      }
    ]
  },
  {
    "key": "insufficientRelief",
    "text": "During these breakout pain or acute episodes, did you find the 22% THC flower insufficient in providing relief?",
    "type": "radio",
    "order": 14,
    "sectionId": "k6f7a8b9-c0d1-4345-f012-678901234567",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "yes-definitely",
        "label": "Yes, definitely",
        "score": 4
      },
      {
        "value": "yes-somewhat",
        "label": "Yes, somewhat",
        "score": 3
      },
      {
        "value": "no-adequate",
        "label": "No, it was adequate",
        "score": 1
      },
      {
        "value": "no-complete-relief",
        "label": "No, it provided complete relief",
        "score": 0
      }
    ]
  },
  {
    "key": "satisfactionWithForm",
    "text": "How satisfied are you with the form of THC flower you used during the trial (e.g., vaporized, smoked)?",
    "type": "radio",
    "order": 15,
    "sectionId": "k6f7a8b9-c0d1-4345-f012-678901234567",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "very-satisfied",
        "label": "Very satisfied",
        "score": 4
      },
      {
        "value": "satisfied",
        "label": "Satisfied",
        "score": 3
      },
      {
        "value": "neutral",
        "label": "Neutral",
        "score": 2
      },
      {
        "value": "unsatisfied",
        "label": "Unsatisfied",
        "score": 1
      },
      {
        "value": "very-unsatisfied",
        "label": "Very unsatisfied",
        "score": 0
      }
    ]
  },
  {
    "key": "openToHigherPotency",
    "text": "Would you be open to trying a higher potency of THC flower (29%)?",
    "type": "radio",
    "order": 16,
    "sectionId": "l7a8b9c0-d1e2-4456-0123-789012345678",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "yes",
        "label": "Yes",
        "score": 4
      },
      {
        "value": "no",
        "label": "No",
        "score": 0
      },
      {
        "value": "maybe",
        "label": "Maybe",
        "score": 2
      }
    ]
  }
]'::jsonb
WHERE id = 'thc_increase';

-- Add the final 2 questions to complete all 16
UPDATE questionnaire_configs
SET questions = questions || '[
  {
    "key": "quickReliefImportance",
    "text": "How important is it for you to have quick relief from your symptoms?",
    "type": "radio",
    "order": 17,
    "sectionId": "l7a8b9c0-d1e2-4456-0123-789012345678",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "very-important",
        "label": "Very important",
        "score": 4
      },
      {
        "value": "important",
        "label": "Important",
        "score": 3
      },
      {
        "value": "neutral",
        "label": "Neutral",
        "score": 2
      },
      {
        "value": "not-important",
        "label": "Not important",
        "score": 1
      }
    ]
  },
  {
    "key": "continueTreatment",
    "text": "How likely are you to continue using cannabinoid treatments as part of your treatment plan?",
    "type": "radio",
    "order": 18,
    "sectionId": "m8b9c0d1-e2f3-4567-1234-890123456789",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "very-likely",
        "label": "Very likely",
        "score": 4
      },
      {
        "value": "likely",
        "label": "Likely",
        "score": 3
      },
      {
        "value": "neutral",
        "label": "Neutral",
        "score": 2
      },
      {
        "value": "unlikely",
        "label": "Unlikely",
        "score": 1
      },
      {
        "value": "very-unlikely",
        "label": "Very unlikely",
        "score": 0
      }
    ]
  },
  {
    "key": "overallSatisfaction",
    "text": "How satisfied are you with the overall experience of using 22% THC flower during the trial?",
    "type": "radio",
    "order": 19,
    "sectionId": "m8b9c0d1-e2f3-4567-1234-890123456789",
    "contributesToScore": true,
    "answerOptions": [
      {
        "value": "very-satisfied",
        "label": "Very satisfied",
        "score": 4
      },
      {
        "value": "satisfied",
        "label": "Satisfied",
        "score": 3
      },
      {
        "value": "neutral",
        "label": "Neutral",
        "score": 2
      },
      {
        "value": "unsatisfied",
        "label": "Unsatisfied",
        "score": 1
      },
      {
        "value": "very-unsatisfied",
        "label": "Very unsatisfied",
        "score": 0
      }
    ]
  }
]'::jsonb
WHERE id = 'thc_increase';

-- FINAL VERIFICATION - All 16 questions with correct scoring
SELECT 'FINAL VERIFICATION - THC Increase Complete:' as status,
       jsonb_array_length(questions) as total_questions,
       jsonb_path_query_array(questions, '$[*].key') as all_question_keys,
       (SELECT SUM((jsonb_array_elements(jsonb_path_query_array(questions, '$[*].answerOptions[*].score'))::text)::int)
        FROM (SELECT MAX((jsonb_array_elements(jsonb_path_query_array(questions, '$[*].answerOptions[*].score'))::text)::int) as max_score
              FROM questionnaire_configs WHERE id = 'thc_increase'
              GROUP BY jsonb_path_query(questions, '$[*].key')) as max_scores) as calculated_max_score
FROM questionnaire_configs
WHERE id = 'thc_increase';
